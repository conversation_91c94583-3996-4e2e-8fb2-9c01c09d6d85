#!/usr/bin/env python3

def reverse_file(input_file, output_file):
    """Reverse the binary content of a file"""
    with open(input_file, 'rb') as f:
        data = f.read()
    
    # Reverse the entire file content
    reversed_data = data[::-1]
    
    with open(output_file, 'wb') as f:
        f.write(reversed_data)
    
    print(f"Reversed {input_file} -> {output_file}")

if __name__ == "__main__":
    reverse_file("corrupt.pdf", "fixed.pdf")
