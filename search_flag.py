#!/usr/bin/env python3
import re

def search_flag_patterns(filename):
    """Search for flag patterns in binary data"""
    with open(filename, 'rb') as f:
        data = f.read()
    
    # Convert to string for pattern matching
    text = data.decode('latin-1')  # Use latin-1 to preserve all bytes
    
    # Search for various flag patterns
    patterns = [
        r'shaktictf\{[^}]+\}',
        r'ftcitkahs\{[^}]+\}',  # reversed
        r'[a-zA-Z0-9_]{8,}',     # potential flag content
    ]
    
    print(f"Searching in {filename}...")
    
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            print(f"Pattern '{pattern}' found:")
            for match in matches:
                print(f"  {match}")
    
    # Also search for the flag pattern in hex
    hex_data = data.hex()
    
    # Search for "shaktictf" in hex
    shakti_hex = "shaktictf".encode().hex()
    shakti_rev_hex = "ftcitkahs".encode().hex()
    
    if shakti_hex in hex_data:
        print(f"Found 'shaktictf' in hex at position: {hex_data.find(shakti_hex)}")
    
    if shakti_rev_hex in hex_data:
        print(f"Found 'ftcitkahs' in hex at position: {hex_data.find(shakti_rev_hex)}")

if __name__ == "__main__":
    search_flag_patterns("corrupt.pdf")
    search_flag_patterns("fixed.pdf")
